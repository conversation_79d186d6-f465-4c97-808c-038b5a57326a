#!/usr/bin/env node

/**
 * 合同审核系统数据库初始化脚本
 * 创建 SQLite 数据库表结构并插入默认数据
 */

const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcrypt');
const path = require('path');
const fs = require('fs');
const DATABASE_CONFIG = require('./src/config/database');

// 使用统一的数据库配置
const DB_PATH = DATABASE_CONFIG.path;
const UPLOAD_DIR = path.join(__dirname, 'uploads');

console.log('🚀 开始初始化合同审核系统数据库...');

// 确保上传目录存在
if (!fs.existsSync(UPLOAD_DIR)) {
  fs.mkdirSync(UPLOAD_DIR, { recursive: true });
  console.log('📁 创建上传目录:', UPLOAD_DIR);
}

// 创建数据库连接
const db = new sqlite3.Database(DB_PATH, (err) => {
  if (err) {
    console.error('❌ 数据库连接失败:', err.message);
    process.exit(1);
  }
  console.log('✅ 数据库连接成功:', DB_PATH);
});

// 数据库初始化函数
async function initDatabase() {
  try {
    // 启用外键约束
    await runQuery('PRAGMA foreign_keys = ON');
    console.log('✅ 启用外键约束');

    // 创建用户表
    await createUsersTable();

    // 创建合同表
    await createContractsTable();

    // 创建操作日志表
    await createOperationLogsTable();

    // 创建通知表
    await createNotificationsTable();

    // 创建合同审核历史表
    await createContractReviewsTable();

    // 插入默认数据
    await insertDefaultData();

    console.log('🎉 数据库初始化完成！');

  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);
    process.exit(1);
  } finally {
    db.close((err) => {
      if (err) {
        console.error('❌ 关闭数据库连接失败:', err.message);
      } else {
        console.log('✅ 数据库连接已关闭');
      }
    });
  }
}

// 执行 SQL 查询的 Promise 封装
function runQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function (err) {
      if (err) {
        reject(err);
      } else {
        resolve({ id: this.lastID, changes: this.changes });
      }
    });
  });
}

// 创建用户表
async function createUsersTable() {
  const sql = `
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT UNIQUE NOT NULL,
      password TEXT NOT NULL,
      role TEXT NOT NULL CHECK (role IN ('employee', 'county_reviewer', 'city_reviewer', 'legal_officer', 'admin')),
      status TEXT DEFAULT 'active' CHECK (status IN ('active', 'banned')),
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      created_by INTEGER,
      FOREIGN KEY (created_by) REFERENCES users(id)
    )
  `;

  await runQuery(sql);
  console.log('✅ 创建用户表 (users)');

  // 创建用户名索引
  await runQuery('CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)');
  await runQuery('CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)');
  console.log('✅ 创建用户表索引');
}

// 创建合同表
async function createContractsTable() {
  const sql = `
    CREATE TABLE IF NOT EXISTS contracts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      serial_number TEXT UNIQUE NOT NULL,
      submitter_id INTEGER NOT NULL,
      reviewer_id INTEGER,
      filename TEXT NOT NULL,
      file_path TEXT NOT NULL,
      file_size INTEGER,
      status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
      submit_note TEXT,
      review_comment TEXT,
      submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      reviewed_at DATETIME,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (submitter_id) REFERENCES users(id),
      FOREIGN KEY (reviewer_id) REFERENCES users(id)
    )
  `;

  await runQuery(sql);
  console.log('✅ 创建合同表 (contracts)');

  // 创建合同表索引
  await runQuery('CREATE INDEX IF NOT EXISTS idx_contracts_serial ON contracts(serial_number)');
  await runQuery('CREATE INDEX IF NOT EXISTS idx_contracts_submitter ON contracts(submitter_id)');
  await runQuery('CREATE INDEX IF NOT EXISTS idx_contracts_reviewer ON contracts(reviewer_id)');
  await runQuery('CREATE INDEX IF NOT EXISTS idx_contracts_status ON contracts(status)');
  console.log('✅ 创建合同表索引');
}

// 创建操作日志表
async function createOperationLogsTable() {
  const sql = `
    CREATE TABLE IF NOT EXISTS operation_logs (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      action TEXT NOT NULL,
      resource_type TEXT NOT NULL,
      resource_id TEXT,
      details TEXT,
      ip_address TEXT,
      user_agent TEXT,
      status TEXT DEFAULT 'success' CHECK (status IN ('success', 'failed')),
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id)
    )
  `;

  await runQuery(sql);
  console.log('✅ 创建操作日志表 (operation_logs)');

  // 创建操作日志表索引
  await runQuery('CREATE INDEX IF NOT EXISTS idx_logs_user ON operation_logs(user_id)');
  await runQuery('CREATE INDEX IF NOT EXISTS idx_logs_action ON operation_logs(action)');
  await runQuery('CREATE INDEX IF NOT EXISTS idx_logs_resource ON operation_logs(resource_type)');
  await runQuery('CREATE INDEX IF NOT EXISTS idx_logs_created ON operation_logs(created_at)');
  console.log('✅ 创建操作日志表索引');
}

// 创建通知表
async function createNotificationsTable() {
  const sql = `
    CREATE TABLE IF NOT EXISTS notifications (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('contract_submitted', 'contract_approved', 'contract_rejected', 'contract_assigned', 'contract_transferred', 'contract_assigned_to_legal_officer', 'contract_number_assigned', 'system_notice')),
      title TEXT NOT NULL,
      content TEXT NOT NULL,
      is_read BOOLEAN DEFAULT 0,
      related_id INTEGER,
      related_type TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      read_at DATETIME,
      FOREIGN KEY (user_id) REFERENCES users(id)
    )
  `;

  await runQuery(sql);
  console.log('✅ 创建通知表 (notifications)');

  // 创建通知表索引
  await runQuery('CREATE INDEX IF NOT EXISTS idx_notifications_user ON notifications(user_id)');
  await runQuery('CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type)');
  await runQuery('CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(is_read)');
  await runQuery('CREATE INDEX IF NOT EXISTS idx_notifications_created ON notifications(created_at)');
  console.log('✅ 创建通知表索引');
}

// 创建合同审核历史表
async function createContractReviewsTable() {
  const sql = `
    CREATE TABLE IF NOT EXISTS contract_reviews (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      contract_id INTEGER NOT NULL,
      reviewer_id INTEGER NOT NULL,
      reviewer_role TEXT NOT NULL CHECK (reviewer_role IN ('county_reviewer', 'city_reviewer', 'admin')),
      result TEXT NOT NULL CHECK (result IN ('approved', 'rejected')),
      comment TEXT,
      review_level TEXT NOT NULL CHECK (review_level IN ('county_reviewer', 'city_reviewer')),
      created_at DATETIME NOT NULL,
      FOREIGN KEY (contract_id) REFERENCES contracts(id) ON DELETE CASCADE,
      FOREIGN KEY (reviewer_id) REFERENCES users(id)
    )
  `;

  await runQuery(sql);
  console.log('✅ 创建合同审核历史表 (contract_reviews)');

  // 创建合同审核历史表索引
  await runQuery('CREATE INDEX IF NOT EXISTS idx_contract_reviews_contract ON contract_reviews(contract_id)');
  await runQuery('CREATE INDEX IF NOT EXISTS idx_contract_reviews_reviewer ON contract_reviews(reviewer_id)');
  await runQuery('CREATE INDEX IF NOT EXISTS idx_contract_reviews_created ON contract_reviews(created_at)');
  console.log('✅ 创建合同审核历史表索引');
}

// 插入默认数据
async function insertDefaultData() {
  console.log('📝 开始插入默认数据...');

  // 生成加密密码 - 使用较低的saltRounds减少CPU负载
  const saltRounds = process.env.NODE_ENV === 'production' ? 6 : 4;
  const adminPassword = await bcrypt.hash('admin123', saltRounds);
  const employeePassword = await bcrypt.hash('123456', saltRounds);
  const countyReviewerPassword = await bcrypt.hash('123456', saltRounds);
  const cityReviewerPassword = await bcrypt.hash('123456', saltRounds);

  // 插入默认管理员
  const adminResult = await runQuery(`
    INSERT OR IGNORE INTO users (username, password, role, status)
    VALUES (?, ?, 'admin', 'active')
  `, ['admin', adminPassword]);

  if (adminResult.changes > 0) {
    console.log('✅ 创建默认管理员账号: admin / admin123');
  }

  // 插入默认员工账号
  const employeeResult = await runQuery(`
    INSERT OR IGNORE INTO users (username, password, role, status, created_by)
    VALUES (?, ?, 'employee', 'active', 1)
  `, ['employee', employeePassword]);

  if (employeeResult.changes > 0) {
    console.log('✅ 创建默认员工账号: employee / 123456');
  }

  // 插入默认县级审核员账号
  const countyReviewerResult = await runQuery(`
    INSERT OR IGNORE INTO users (username, password, role, status, created_by)
    VALUES (?, ?, 'county_reviewer', 'active', 1)
  `, ['county_reviewer', countyReviewerPassword]);

  if (countyReviewerResult.changes > 0) {
    console.log('✅ 创建默认县级审核员账号: county_reviewer / 123456');
  }

  // 插入默认市级审核员账号
  const cityReviewerResult = await runQuery(`
    INSERT OR IGNORE INTO users (username, password, role, status, created_by)
    VALUES (?, ?, 'city_reviewer', 'active', 1)
  `, ['city_reviewer', cityReviewerPassword]);

  if (cityReviewerResult.changes > 0) {
    console.log('✅ 创建默认市级审核员账号: city_reviewer / 123456');
  }

  // 插入默认法规员账号
  const legalOfficerPassword = await bcrypt.hash('123456', saltRounds);
  const legalOfficerResult = await runQuery(`
    INSERT OR IGNORE INTO users (username, password, role, status, created_by)
    VALUES (?, ?, 'legal_officer', 'active', 1)
  `, ['legal_officer', legalOfficerPassword]);

  if (legalOfficerResult.changes > 0) {
    console.log('✅ 创建默认法规员账号: legal_officer / 123456');
  }

  // 插入更多测试用户
  await insertTestUsers();

  console.log('✅ 默认数据插入完成');
}

// 插入测试用户数据
async function insertTestUsers() {
  const testUsers = [
    { username: 'employee2', role: 'employee' },
    { username: 'employee3', role: 'employee' },
    { username: 'county_reviewer2', role: 'county_reviewer' },
    { username: 'test_user', role: 'employee' }
  ];

  // 使用较低的saltRounds减少CPU负载
  const saltRounds = process.env.NODE_ENV === 'production' ? 6 : 4;
  const defaultPassword = await bcrypt.hash('123456', saltRounds);

  for (const user of testUsers) {
    try {
      await runQuery(`
        INSERT OR IGNORE INTO users (username, password, role, status, created_by)
        VALUES (?, ?, ?, 'active', 1)
      `, [user.username, defaultPassword, user.role]);

      console.log(`✅ 创建测试用户: ${user.username} (${user.role})`);
    } catch (error) {
      console.log(`⚠️  用户 ${user.username} 已存在，跳过创建`);
    }
  }
}

// 主函数
if (require.main === module) {
  initDatabase();
}

module.exports = {
  initDatabase,
  DB_PATH
};
