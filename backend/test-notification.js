/**
 * 测试通知发送功能
 * 用于调试法规员通知问题
 */

const { UserModel } = require('./src/utils/database');
const NotificationService = require('./src/utils/notification');
const { USER_ROLES } = require('./src/utils/constants');

async function testNotification() {
  try {
    console.log('🔍 开始测试通知发送功能...');
    
    // 1. 测试查找法规员
    console.log('\n1. 查找法规员用户:');
    const legalOfficers = await UserModel.findByRole(USER_ROLES.LEGAL_OFFICER);
    console.log('找到的法规员:', legalOfficers);
    
    if (!legalOfficers || legalOfficers.length === 0) {
      console.error('❌ 没有找到法规员用户');
      return;
    }
    
    // 2. 测试通知发送
    console.log('\n2. 测试发送通知:');
    const contractData = {
      id: 3,
      serial_number: 'HT003',
      submitter_name: 'test_user',
      filename: 'test.pdf'
    };
    
    for (const legalOfficer of legalOfficers) {
      console.log(`向法规员 ${legalOfficer.username} (ID: ${legalOfficer.id}) 发送通知...`);
      try {
        const result = await NotificationService.notifyContractAssignedToLegalOfficer(
          contractData, 
          legalOfficer.id
        );
        console.log('✅ 通知发送成功:', result);
      } catch (error) {
        console.error('❌ 通知发送失败:', error);
      }
    }
    
    // 3. 验证通知是否创建成功
    console.log('\n3. 验证通知创建:');
    const { NotificationModel } = require('./src/utils/database');
    for (const legalOfficer of legalOfficers) {
      const notifications = await NotificationModel.getByUserId(legalOfficer.id, {}, 1, 5);
      console.log(`法规员 ${legalOfficer.username} 的通知:`, notifications);
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
testNotification().then(() => {
  console.log('\n✅ 测试完成');
  process.exit(0);
}).catch(error => {
  console.error('❌ 测试失败:', error);
  process.exit(1);
});
